package api

import (
	"strings"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/action"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/oms"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"gitlab.buymed.tech/sdk/go-sdk-v2/server"
)

func QueryOrderList(req server.APIRequest, resp server.APIResponder) error {
	var input request.QueryInput[request.OrderQuery, request.QueryOption]

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	if input.Limit < 0 || input.Limit > 1000 {
		input.Limit = 1000
	}

	if input.Offset < 0 {
		input.Offset = 0
	}

	actionSource := utils.GetActionSource(req)
	if actionSource == nil {
		// Respond with a permission denied error if action source is not found
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "Permission denied",
		})
	}

	return resp.Respond(action.QueryOrderList(&input.Q, input.Search, input.Offset, input.Limit, input.Option, actionSource))
}

// CreateOrder ...
func CreateOrder(req server.APIRequest, resp server.APIResponder) error {
	var input request.CreateOrderRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	return resp.Respond(action.CreateOrder(&input, utils.GetActionSource(req)))
}

func RemoveOrderDraft(req server.APIRequest, resp server.APIResponder) error {
	params := req.GetParams()

	return resp.Respond(action.RemoveOrderDraft(params["orderCode"], utils.GetActionSource(req)))
}

// QueryOrderBidList ...
func QueryOrderBidList(req server.APIRequest, resp server.APIResponder) error {
	var input request.QueryInput[request.OrderBidQuery, request.QueryOption]

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	if input.Limit > 1000 {
		input.Limit = 1000
	}

	actionSource := utils.GetActionSource(req)
	if actionSource == nil {
		// Respond with a permission denied error if action source is not found
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "Permission denied",
		})
	}

	return resp.Respond(action.QueryOrderBidList(&input.Q, input.Search, input.Offset, input.Limit, input.Option, actionSource))
}

// UpdateOrderStatus ...
func UpdateOrderStatus(req server.APIRequest, resp server.APIResponder) error {
	var input request.UpdateOrderStatusRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	if acc := utils.GetActionSource(req); acc != nil {
		return resp.Respond(action.UpdateOrderStatus(&input, utils.GetActionSource(req)))
	}

	return resp.Respond(&common.APIResponse[any]{
		Status:  common.APIStatus.Unauthorized,
		Message: "Tài khoản của bạn không thể thực hiện thao tác này",
	})
}

// GetOrderInfo ...
func GetOrderInfo(req server.APIRequest, resp server.APIResponder) error {
	params := req.GetParams()
	var orderCode = params["code"]

	arrParams := strings.Split(params["option"], ",")
	optionContract, optionBid := false, false
	for _, item := range arrParams {
		if item == "contract" {
			optionContract = true
			continue
		}
		if item == "bid" {
			optionBid = true
			continue
		}
	}

	if acc := utils.GetActionSource(req); acc != nil {
		return resp.Respond(action.GetOrderInfo(orderCode, &request.GetOrderInfoQuery{
			Contract: optionContract,
			Bid:      optionBid,
		}, acc))
	}

	return resp.Respond(&common.APIResponse[any]{
		Status:  common.APIStatus.Unauthorized,
		Message: "Tài khoản của bạn không thể thực hiện thao tác này",
	})
}

func UpdateSaleOrderFulfillment(req server.APIRequest, resp server.APIResponder) error {
	var input request.UpdateSaleOrderFulfillmentRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	if acc := utils.GetActionSource(req); acc != nil {
		return resp.Respond(action.UpdateSaleOrderFulfillment(&input, acc))
	}

	return resp.Respond(&common.APIResponse[any]{
		Status:  common.APIStatus.Unauthorized,
		Message: "Tài khoản của bạn không thể thực hiện thao tác này",
	})
}

func CancelOrder(req server.APIRequest, resp server.APIResponder) error {
	var input oms.CancelOrderRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}
	return resp.Respond(action.CancelOrder(&input, utils.GetActionSource(req)))
}

func RequestExportInvoiceExchange(req server.APIRequest, resp server.APIResponder) error {
	params := req.GetParams()
	var orderCode = params["code"]
	return resp.Respond(action.RequestExportInvoiceExchange(orderCode, utils.GetActionSource(req)))
}

func MigrateCancelOrder(req server.APIRequest, resp server.APIResponder) error {
	params := req.GetParams()
	var orderCode = params["orderCode"]

	return resp.Respond(action.MigrateCancelOrderByOrderCode(orderCode))
}
