package api

import (
	"gitlab.buymed.tech/buymed.com/tender/core-tender/action"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/server"
)

// Tool update the contract tax code
func ToolUpdateContractTaxCode(req server.APIRequest, resp server.APIResponder) error {
	var input request.UpdateContractTaxCodeRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}
	return resp.Respond(action.ToolUpdateContractTaxCode(&input, utils.GetActionSource(req)))
}

// Tool update the contract tax code
func ToolUpdateOrderTaxCode(req server.APIRequest, resp server.APIResponder) error {
	var input request.UpdateOrderTaxCodeRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}
	return resp.Respond(action.ToolUpdateOrderTaxCode(&input, utils.GetActionSource(req)))
}

// Tool update the contract tax code
func ToolSyncOrderInvoice(req server.APIRequest, resp server.APIResponder) error {
	var input request.SyncOrderInvoiceRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}
	return resp.Respond(action.ToolSyncOrderInvoice(&input, utils.GetActionSource(req)))
}

// Tool cancel order without order status.
func ToolForceCancelOrder(req server.APIRequest, resp server.APIResponder) error {
	var input request.ForceCancelOrderRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}
	return resp.Respond(action.ToolForceCancelOrder(&input, utils.GetActionSource(req)))
}
