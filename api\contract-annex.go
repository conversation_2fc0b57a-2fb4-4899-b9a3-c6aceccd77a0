package api

import (
	"gitlab.buymed.tech/buymed.com/tender/core-tender/action"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/server"
)

// QueryContractAnnexList ...
func QueryContractAnnexList(req server.APIRequest, resp server.APIResponder) error {
	var input request.QueryInput[request.ContractAnnexQuery, request.QueryOption]

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	if input.Limit > 1000 {
		input.Limit = 1000
	}

	return resp.Respond(action.QueryContractAnnexList(&input.Q, input.Search, input.Offset, input.Limit, input.Option))
}

// CreateContractAnnex ...
func CreateContractAnnex(req server.APIRequest, resp server.APIResponder) error {
	var input request.CreateContractAnnexRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	return resp.Respond(action.CreateContractAnnex(&input, utils.GetActionSource(req)))
}

// GetContractAnnexInfo ...
func GetContractAnnexInfo(req server.APIRequest, resp server.APIResponder) error {
	params := req.GetParams()

	var (
		contractCode = params["contractCode"]
		code         = params["code"]
	)
	return resp.Respond(action.GetContractAnnexInfo(contractCode, code))
}

// UpdateContractAnnex ...
func UpdateContractAnnex(req server.APIRequest, resp server.APIResponder) error {
	var input request.UpdateContractAnnexRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}
	return resp.Respond(action.UpdateContractAnnex(&input, utils.GetActionSource(req)))
}
