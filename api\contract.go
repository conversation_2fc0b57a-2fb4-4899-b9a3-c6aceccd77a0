package api

import (
	"strings"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/action"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"gitlab.buymed.tech/sdk/go-sdk-v2/server"
)

// GetContractInfo ...
func GetContractInfo(req server.APIRequest, resp server.APIResponder) error {
	params := req.GetParams()
	var contractID = utils.ParseInt64(params["contractID"], 0)
	var contractCode = params["code"]
	var options = strings.Split(params["option"], ",")

	if acc := utils.GetActionSource(req); acc != nil {
		return resp.Respond(action.GetContractInfo(contractID, contractCode, options, acc))
	}

	return resp.Respond(&common.APIResponse[any]{
		Status:  common.APIStatus.Unauthorized,
		Message: "<PERSON><PERSON><PERSON> kho<PERSON>n của bạn không thể thực hiện thao tác này",
	})
}

// CreateContract ...
func CreateContract(req server.APIRequest, resp server.APIResponder) error {
	var input request.CreateContractRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	if acc := utils.GetActionSource(req); acc != nil {
		return resp.Respond(action.CreateContract(&input, utils.GetActionSource(req)))
	}

	return resp.Respond(&common.APIResponse[any]{
		Status:  common.APIStatus.Unauthorized,
		Message: "Tài khoản của bạn không thể thực hiện thao tác này",
	})
}

// QueryContractList ...
func QueryContractList(req server.APIRequest, resp server.APIResponder) error {
	var input request.QueryInput[request.ContractQuery, request.QueryOption]

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	if input.Limit > 1000 {
		input.Limit = 1000
	}

	actionSource := utils.GetActionSource(req)
	if actionSource == nil {
		// Respond with a permission denied error if action source is not found
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "Permission denied",
		})
	}

	return resp.Respond(action.QueryContractList(&input.Q, input.Search, input.Offset, input.Limit, input.Option, actionSource))
}

// UpdateContract ...
func UpdateContract(req server.APIRequest, resp server.APIResponder) error {
	var input request.UpdateContractRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}
	return resp.Respond(action.UpdateContract(&input, utils.GetActionSource(req)))
}
