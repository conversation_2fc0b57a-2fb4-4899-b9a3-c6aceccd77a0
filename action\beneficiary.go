package action

import (
	"fmt"
	"regexp"
	"strings"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/model"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func CreateBeneficiary(input *request.CreateBeneficiaryRequest, actionSource *request.ActionSource) common.Response {
	if checkResponse := input.Validate(); !checkResponse.Ok() {
		return checkResponse
	}

	input.TaxCode = strings.TrimSpace(input.TaxCode)
	input.TaxCode = strings.Replace(input.TaxCode, " ", "", -1)
	input.TaxCode = strings.Replace(input.TaxCode, `\t`, ``, -1)
	input.TaxCode = strings.Replace(input.TaxCode, `\n`, ``, -1)
	input.TaxCode = utils.NormalizeTaxCode(input.TaxCode)
	err := CheckTax(input.TaxCode)
	if err != nil {
		return utils.InvalidResponse(err.Error(), "INVALID_TAX_CODE")
	}
	beneficiaryResp := model.BeneficiaryDB.QueryOne(&model.Beneficiary{
		TaxCode: input.TaxCode,
	})
	if beneficiaryResp.Ok() {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Error,
			Message:   "Tax code has existed",
			ErrorCode: "EXISTED_TAX_CODE",
		}
	}
	newBeneficiaryID := model.GenBeneficiaryID()
	newBeneficiary := &model.Beneficiary{
		Name:          input.Name,
		Address:       input.Address,
		PhoneNumber:   input.PhoneNumber,
		TaxCode:       input.TaxCode,
		Status:        input.Status,
		CreatedByID:   actionSource.Account.AccountID,
		CreatedByName: actionSource.Account.FullName,
		BeneficiaryID: newBeneficiaryID,
		Code:          model.ConvertToCode(newBeneficiaryID),
	}
	newBeneficiary.HashTag = model.GenBeneficiaryHashTag(newBeneficiary)
	create := model.BeneficiaryDB.Create(newBeneficiary)
	return create
}

// QueryBeneficiaryList ...
func QueryBeneficiaryList(query *request.BeneficiaryQuery, search string, offset, limit int64, queryOptions request.QueryOption, actionSource *request.ActionSource) common.Response {
	if search != "" {
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"hash_tag": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", regexp.QuoteMeta(utils.ParserQ(search))), Options: ""},
		})
	}

	if len(query.BeneficiaryIDs) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"beneficiary_id": bson.M{
				"$in": utils.Unique(query.BeneficiaryIDs),
			},
		})
	} else if len(query.BeneficiaryCodes) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"code": bson.M{
				"$in": utils.Unique(query.BeneficiaryCodes),
			},
		})
	}

	// Generate query scope based on action source
	scope := utils.GenQueryScope(*actionSource)
	if scope != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"scope": bson.M{"$in": scope},
		})
	}

	resp := model.BeneficiaryDB.Query(query, offset, limit, &primitive.D{{Key: "_id", Value: -1}})
	if !resp.Ok() {
		return resp
	}

	if queryOptions.Total {
		resp.Total = model.BeneficiaryDB.Count(query).Total
	}
	return resp
}
